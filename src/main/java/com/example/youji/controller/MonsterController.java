package com.example.youji.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.youji.common.Result;
import com.example.youji.dto.MonsterDTO;
import com.example.youji.entity.MonsterCategory;
import com.example.youji.service.MonsterCategoryService;
import com.example.youji.service.MonsterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 妖怪控制器
 */
@RestController
@RequestMapping("/api/monsters")
public class MonsterController {
    
    @Autowired
    private MonsterService monsterService;
    
    @Autowired
    private MonsterCategoryService monsterCategoryService;
    
    /**
     * 分页查询妖怪列表
     */
    @GetMapping("/page")
    public Result<IPage<MonsterDTO>> getMonsterPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String keyword) {
        
        IPage<MonsterDTO> page = monsterService.getMonsterPage(current, size, categoryId, keyword);
        return Result.success(page);
    }
    
    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public Result<List<MonsterCategory>> getCategories() {
        List<MonsterCategory> categories = monsterCategoryService.list();
        return Result.success(categories);
    }
}
