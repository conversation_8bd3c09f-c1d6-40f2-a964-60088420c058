package com.example.youji.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 妖怪实体类
 */
@Data
@TableName("monsters")
public class Monster {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 妖怪名称
     */
    private String name;

    /**
     * 相关诗句
     */
    private String poem;

    /**
     * 故事描述
     */
    private String story;

    /**
     * 图片路径
     */
    private String imagePath;
}
