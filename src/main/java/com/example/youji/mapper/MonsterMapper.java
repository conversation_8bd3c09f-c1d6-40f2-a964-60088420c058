package com.example.youji.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.youji.dto.MonsterDTO;
import com.example.youji.entity.Monster;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 妖怪Mapper接口
 */
@Mapper
public interface MonsterMapper extends BaseMapper<Monster> {
    
    /**
     * 分页查询妖怪信息（包含分类名称）
     */
    @Select("SELECT m.*, mc.name as category_name " +
            "FROM monsters m " +
            "LEFT JOIN monster_categories mc ON m.category_id = mc.id " +
            "WHERE (#{categoryId} IS NULL OR m.category_id = #{categoryId}) " +
            "AND (#{keyword} IS NULL OR #{keyword} = '' OR m.name LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR m.poem LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR m.story LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY m.id DESC")
    IPage<MonsterDTO> selectMonsterPage(Page<MonsterDTO> page, 
                                       @Param("categoryId") Integer categoryId, 
                                       @Param("keyword") String keyword);
}
