package com.example.youji.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.youji.dto.MonsterDTO;
import com.example.youji.entity.Monster;

/**
 * 妖怪服务接口
 */
public interface MonsterService extends IService<Monster> {
    
    /**
     * 分页查询妖怪信息
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param categoryId 分类ID
     * @param keyword 关键词
     * @return 分页结果
     */
    IPage<MonsterDTO> getMonsterPage(long current, long size, Integer categoryId, String keyword);
}
