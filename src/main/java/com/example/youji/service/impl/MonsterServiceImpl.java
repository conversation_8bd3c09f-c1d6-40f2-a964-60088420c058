package com.example.youji.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.youji.dto.MonsterDTO;
import com.example.youji.entity.Monster;
import com.example.youji.mapper.MonsterMapper;
import com.example.youji.service.MonsterService;
import org.springframework.stereotype.Service;

/**
 * 妖怪服务实现类
 */
@Service
public class MonsterServiceImpl extends ServiceImpl<MonsterMapper, Monster> implements MonsterService {
    
    @Override
    public IPage<MonsterDTO> getMonsterPage(long current, long size, Integer categoryId, String keyword) {
        Page<MonsterDTO> page = new Page<>(current, size);
        return baseMapper.selectMonsterPage(page, categoryId, keyword);
    }
}
