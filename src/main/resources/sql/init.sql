-- 创建数据库
CREATE DATABASE IF NOT EXISTS youji_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE youji_db;

-- 妖怪分类表
CREATE TABLE IF NOT EXISTS monster_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '分类名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='妖怪分类表';

-- 妖怪表
CREATE TABLE IF NOT EXISTS monsters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '妖怪名称',
    poem TEXT COMMENT '相关诗句',
    story TEXT COMMENT '故事描述',
    image_path VARCHAR(255) COMMENT '图片路径',
    FOREIGN KEY (category_id) REFERENCES monster_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='妖怪表';

-- 插入分类数据
INSERT INTO monster_categories (name) VALUES
('神仙类'),
('妖精类'),
('鬼怪类'),
('龙族类'),
('佛门类'),
('道门类');

-- 插入妖怪数据
INSERT INTO monsters (category_id, name, poem, story, image_path) VALUES
(1, '太上老君', '炉中日月长不夜，壶里乾坤别有天', '道教最高神明之一，居住在兜率宫，炼制仙丹，是孙悟空的师父之一。', '/images/taishanglaojun.png'),
(2, '白骨精', '千年白骨化精灵，巧语花言惑圣僧', '白虎岭上的白骨妖精，善于变化，曾三次变化欺骗唐僧师徒。', '/images/baigujing.png'),
(2, '蜘蛛精', '丝网千重困行者，美色一现迷心神', '盘丝洞七个蜘蛛精，善于吐丝结网，曾困住唐僧师徒。', '/images/zhizhujing.png'),
(3, '黑熊精', '黑风山上黑熊怪，偷袈裟来显神通', '黑风山的熊妖，武艺高强，曾偷取唐僧的袈裟。', '/images/heixiongjing.png'),
(4, '龙王', '兴云布雨泽苍生，四海龙王掌水程', '东海龙王敖广，掌管海洋和降雨，孙悟空曾向其借取如意金箍棒。', '/images/longwang.png'),
(5, '观音菩萨', '慈悲普度众生苦，紫竹林中现真身', '西方极乐世界观世音菩萨，慈悲为怀，多次帮助唐僧师徒化解危难。', '/images/guanyin.png');
