$(document).ready(function() {
    let currentPage = 1;
    const pageSize = 6;
    
    // 初始化
    init();
    
    function init() {
        loadCategories();
        loadMonsters();
        bindEvents();
    }
    
    // 绑定事件
    function bindEvents() {
        // 搜索按钮点击事件
        $('#searchBtn').click(function() {
            currentPage = 1;
            loadMonsters();
        });
        
        // 回车搜索
        $('#keywordInput').keypress(function(e) {
            if (e.which === 13) {
                currentPage = 1;
                loadMonsters();
            }
        });
        
        // 分类选择变化
        $('#categorySelect').change(function() {
            currentPage = 1;
            loadMonsters();
        });
    }
    
    // 加载分类数据
    function loadCategories() {
        $.ajax({
            url: '/api/monsters/categories',
            type: 'GET',
            success: function(response) {
                if (response.code === 200) {
                    const categories = response.data;
                    let options = '<option value="">全部分类</option>';
                    categories.forEach(function(category) {
                        options += `<option value="${category.id}">${category.name}</option>`;
                    });
                    $('#categorySelect').html(options);
                }
            },
            error: function() {
                console.error('加载分类失败');
            }
        });
    }
    
    // 加载妖怪数据
    function loadMonsters() {
        const categoryId = $('#categorySelect').val();
        const keyword = $('#keywordInput').val().trim();
        
        // 显示加载状态
        $('#monsterList').html('<div class="col-12"><div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</div></div>');
        
        $.ajax({
            url: '/api/monsters/page',
            type: 'GET',
            data: {
                current: currentPage,
                size: pageSize,
                categoryId: categoryId || null,
                keyword: keyword || null
            },
            success: function(response) {
                if (response.code === 200) {
                    const pageData = response.data;
                    renderMonsters(pageData.records);
                    renderPagination(pageData);
                } else {
                    $('#monsterList').html('<div class="col-12"><div class="no-data">加载失败：' + response.message + '</div></div>');
                }
            },
            error: function() {
                $('#monsterList').html('<div class="col-12"><div class="no-data">网络错误，请稍后重试</div></div>');
            }
        });
    }
    
    // 渲染妖怪列表
    function renderMonsters(monsters) {
        if (!monsters || monsters.length === 0) {
            $('#monsterList').html('<div class="col-12"><div class="no-data"><i class="fas fa-ghost"></i><br>暂无妖怪数据</div></div>');
            return;
        }
        
        let html = '';
        monsters.forEach(function(monster) {
            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="monster-card">
                        <div class="monster-image">
                            <i class="fas fa-dragon"></i>
                        </div>
                        <div class="monster-content">
                            <div class="monster-name">${monster.name}</div>
                            <div class="monster-category">${monster.categoryName || '未分类'}</div>
                            ${monster.poem ? `<div class="monster-poem">"${monster.poem}"</div>` : ''}
                            <div class="monster-story">${monster.story || '暂无故事描述'}</div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        $('#monsterList').html(html);
    }
    
    // 渲染分页
    function renderPagination(pageData) {
        const totalPages = pageData.pages;
        const current = pageData.current;
        
        if (totalPages <= 1) {
            $('#pagination').html('');
            return;
        }
        
        let html = '';
        
        // 上一页
        if (current > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${current - 1}">上一页</a></li>`;
        } else {
            html += `<li class="page-item disabled"><span class="page-link">上一页</span></li>`;
        }
        
        // 页码
        const startPage = Math.max(1, current - 2);
        const endPage = Math.min(totalPages, current + 2);
        
        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            if (i === current) {
                html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                html += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
            }
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`;
        }
        
        // 下一页
        if (current < totalPages) {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${current + 1}">下一页</a></li>`;
        } else {
            html += `<li class="page-item disabled"><span class="page-link">下一页</span></li>`;
        }
        
        $('#pagination').html(html);
        
        // 绑定分页点击事件
        $('#pagination a.page-link').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page && page !== currentPage) {
                currentPage = page;
                loadMonsters();
                $('html, body').animate({scrollTop: 0}, 300);
            }
        });
    }
});
