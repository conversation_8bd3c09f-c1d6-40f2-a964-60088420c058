<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>妖怪生平录</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: white;
            text-align: center;
            margin: 0;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .search-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .monster-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .monster-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .monster-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 48px;
        }
        
        .monster-content {
            padding: 20px;
        }
        
        .monster-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .monster-category {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-bottom: 15px;
        }
        
        .monster-poem {
            font-style: italic;
            color: #666;
            margin-bottom: 15px;
            border-left: 3px solid #667eea;
            padding-left: 15px;
            background: rgba(102, 126, 234, 0.05);
            padding: 10px 15px;
            border-radius: 5px;
        }
        
        .monster-story {
            color: #555;
            line-height: 1.6;
        }
        
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2rem;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-dragon"></i> 妖怪生平录 <i class="fas fa-dragon"></i></h1>
    </div>

    <div class="container">
        <!-- 搜索面板 -->
        <div class="search-panel">
            <div class="row">
                <div class="col-md-4">
                    <label for="categorySelect">妖怪分类：</label>
                    <select id="categorySelect" class="form-control">
                        <option value="">全部分类</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="keywordInput">关键词搜索：</label>
                    <input type="text" id="keywordInput" class="form-control" placeholder="请输入妖怪名称、诗句或故事关键词">
                </div>
                <div class="col-md-2">
                    <label>&nbsp;</label>
                    <button id="searchBtn" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 妖怪列表 -->
        <div id="monsterList" class="row">
            <!-- 动态加载内容 -->
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
            <nav>
                <ul id="pagination" class="pagination">
                    <!-- 动态加载分页 -->
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
    <script src="/js/monster.js"></script>
</body>
</html>
