@echo off
chcp 65001
echo ====================================
echo 妖怪生平录系统启动脚本
echo ====================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请先安装JDK 8或更高版本
    pause
    exit /b 1
)

echo 正在检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请先安装Maven
    pause
    exit /b 1
)

echo.
echo 环境检查完成，开始启动项目...
echo.

echo 正在编译项目...
call mvn clean compile

if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo.
echo 正在启动Spring Boot应用...
echo 请确保MySQL数据库已启动，并且配置正确
echo.

call mvn spring-boot:run

pause
